version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - AKENEO_ENDPOINT=${AKENEO_ENDPOINT}
        - AKENEO_CLIENT_ID=${AKENEO_CLIENT_ID}
        - AKENEO_CLIENT_SECRET=${AKENEO_CLIENT_SECRET}
        - AKENEO_USERNAME=${AKENEO_USERNAME}
        - AKENEO_PASSWORD=${AKENEO_PASSWORD}
        - AUTH_USERNAME=${AUTH_USERNAME:-christian}
        - AUTH_PASSWORD=${AUTH_PASSWORD:-akeneo-is-cool}
    ports:
      - "5001:3000"
    environment:
      - FLASK_APP=app.py
      - FLASK_ENV=development
      - FLASK_DEBUG=1
      - PORT=3000
      - AKENEO_ENDPOINT=${AKENEO_ENDPOINT}
      - AKENEO_CLIENT_ID=${AKENEO_CLIENT_ID}
      - AKEN<PERSON>O_CLIENT_SECRET=${AKENEO_CLIENT_SECRET}
      - AKENEO_USERNAME=${AKENEO_USERNAME}
      - AKENEO_PASSWORD=${AKENEO_PASSWORD}
      - AUTH_USERNAME=${AUTH_USERNAME:-christian}
      - AUTH_PASSWORD=${AUTH_PASSWORD:-akeneo-is-cool}
      - GOOGLE_MAPPING_SHEET_ID=${GOOGLE_MAPPING_SHEET_ID}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI}
      - GSHEET_SECRET=${GSHEET_SECRET}
      - GOOGLE_CREDENTIALS_FILE=${GOOGLE_CREDENTIALS_FILE}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - DISABLE_LLM_LOGGING=${DISABLE_LLM_LOGGING:-false}
      - REDIS_CONNECTION_STRING=redis://redis:6379/0
    volumes:
      - .:/app
      - ./config:/app/config
      - app_data:/app/data
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - akeneo-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - akeneo-network

volumes:
  app_data:
  redis_data:

networks:
  akeneo-network:
    driver: bridge
